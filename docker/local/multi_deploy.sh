#!/bin/bash

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <GPU卡号列表>"
    echo "示例: $0 0       # 在GPU卡0上启动"
    echo "示例: $0 0,1     # 在GPU卡0和1上分别启动"
    echo "示例: $0 0,1,2,3 # 在GPU卡0,1,2,3上分别启动"
    exit 1
fi

GPU_LIST=$1

# 将GPU列表转换为数组
IFS=',' read -ra GPU_ARRAY <<< "$GPU_LIST"

echo "准备在以下GPU卡上启动容器: ${GPU_ARRAY[*]}"

# 记录启动的容器信息
STARTED_CONTAINERS=()
STARTED_PORTS=()

# 在每张卡上启动容器
for GPU_ID in "${GPU_ARRAY[@]}"; do
    # 去除空格
    GPU_ID=$(echo $GPU_ID | xargs)
    
    # 验证GPU卡是否存在
    if ! nvidia-smi -i $GPU_ID &>/dev/null; then
        echo "警告: GPU卡 $GPU_ID 不存在或不可用，跳过"
        continue
    fi
    
    # 计算端口号
    PORT=$((30050 + GPU_ID))
    
    # 检查端口是否被占用
    if netstat -tuln | grep -q ":$PORT "; then
        echo "警告: 端口 $PORT 已被占用，跳过GPU卡 $GPU_ID"
        continue
    fi
    
    echo "在GPU卡 $GPU_ID 上启动容器，使用端口 $PORT ..."
    
    # 启动容器并获取容器ID
    CONTAINER_ID=$(docker run --gpus all \
    --shm-size 24g \
    -p $PORT:$PORT \
    -e CUDA_VISIBLE_DEVICES=$GPU_ID \
    --ipc=host \
    --name mineru-gpu-$GPU_ID \
    -itd artifacts.iflytek.com/docker-private/datahub/lynxiao/mineru-sglang:v1.0.1 \
    mineru-api --host 0.0.0.0 --port $PORT --enable-torch-compile)
    
    if [ $? -eq 0 ]; then
        echo "✓ GPU卡 $GPU_ID 上的容器启动成功 (容器ID: ${CONTAINER_ID:0:12})"
        echo "  服务地址: http://localhost:$PORT"
        STARTED_CONTAINERS+=("mineru-gpu-$GPU_ID")
        STARTED_PORTS+=("$PORT")
    else
        echo "✗ GPU卡 $GPU_ID 上的容器启动失败"
    fi
    
    echo ""
done

# 显示启动结果摘要
echo "========== 启动摘要 =========="
if [ ${#STARTED_CONTAINERS[@]} -eq 0 ]; then
    echo "没有成功启动任何容器"
else
    echo "成功启动 ${#STARTED_CONTAINERS[@]} 个容器:"
    for i in "${!STARTED_CONTAINERS[@]}"; do
        echo "  ${STARTED_CONTAINERS[i]} -> http://localhost:${STARTED_PORTS[i]}"
    done
    
    echo ""
    echo "管理命令："
    echo "查看所有容器状态: docker ps -f name=mineru-gpu"
    echo "停止所有容器: docker stop ${STARTED_CONTAINERS[*]}"
    echo "删除所有容器: docker rm ${STARTED_CONTAINERS[*]}"
fi