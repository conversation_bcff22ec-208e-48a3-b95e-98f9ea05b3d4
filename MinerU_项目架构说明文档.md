# MinerU 项目架构说明文档

## 🎯 项目概述

MinerU是一个高质量的PDF文档解析工具，专门将PDF文档转换为机器可读的格式（如Markdown，JSON等）。该项目在InternLM预训练过程中开发，重点解决科学文献中的符号转换问题。

### 主要特性
- 🔄 **多种解析后端**：支持传统pipeline和VLM（视觉语言模型）两种解析方式
- 🌍 **多语言支持**：支持84种语言的OCR识别
- 📊 **全面解析**：支持文本、表格、公式、图像等多种内容类型
- ⚡ **高性能**：通过SGLang后端实现20-30倍加速
- 🎨 **多格式输出**：支持Markdown、JSON、HTML等多种输出格式

---

## 📁 项目结构概览

```
MinerU/
├── mineru/                    # 核心代码包
│   ├── __init__.py
│   ├── version.py             # 版本信息
│   ├── cli/                   # 命令行接口
│   │   ├── client.py          # 主要CLI入口
│   │   ├── common.py          # 通用处理函数
│   │   ├── fast_api.py        # FastAPI服务
│   │   ├── gradio_app.py      # Gradio Web界面
│   │   └── models_download.py # 模型下载工具
│   ├── backend/               # 解析后端
│   │   ├── pipeline/          # Pipeline后端（多模型流水线）
│   │   └── vlm/              # VLM后端（视觉语言模型）
│   ├── model/                 # 各种AI模型
│   │   ├── layout/           # 布局检测模型
│   │   ├── ocr/              # OCR模型
│   │   ├── mfd/              # 公式检测模型
│   │   ├── mfr/              # 公式识别模型
│   │   ├── table/            # 表格识别模型
│   │   ├── reading_order/    # 阅读顺序排序
│   │   ├── vlm_hf_model/     # HuggingFace VLM模型
│   │   └── vlm_sglang_model/ # SGLang VLM模型
│   ├── utils/                 # 工具函数
│   ├── data/                 # 数据处理模块
│   └── resources/            # 资源文件
├── demo/                      # 演示文件和结果
├── docs/                      # 文档
├── docker/                    # Docker配置
├── projects/                  # 扩展项目
├── tests/                     # 测试文件
├── pyproject.toml            # Python项目配置
└── mineru.template.json      # 配置模板
```

---

## 🔧 核心架构设计

### 1. 入口点设计

项目通过 `pyproject.toml` 定义了多个命令行入口点：

```toml
[project.scripts]
mineru = "mineru.cli:client.main"                    # 主命令
mineru-sglang-server = "mineru.cli.vlm_sglang_server:main"  # SGLang服务
mineru-models-download = "mineru.cli.models_download:download_models"  # 模型下载
mineru-api = "mineru.cli.fast_api:main"             # API服务
mineru-gradio = "mineru.cli.gradio_app:main"        # Web界面
```

### 2. 主要处理流程

#### 2.1 Pipeline后端流程（传统多模型方法）

```mermaid
graph TD
    A[PDF输入] --> B[文档预处理]
    B --> C[布局检测]
    C --> D[内容分类]
    D --> E[文本块]
    D --> F[图像块] 
    D --> G[表格块]
    D --> H[公式块]
    E --> I[OCR识别]
    F --> J[图像提取]
    G --> K[表格识别]
    H --> L[公式识别]
    I --> M[内容合并]
    J --> M
    K --> M
    L --> M
    M --> N[阅读顺序排序]
    N --> O[多格式输出]
```

**关键步骤说明：**
- **文档预处理**：文档分类、元数据提取、乱码检测、扫描文档识别
- **布局检测**：使用DocLayout-YOLO识别文档结构元素
- **专业化处理**：不同类型内容使用专门的模型处理
- **后处理**：内容合并、布局排序、段落合并、坐标修正

#### 2.2 VLM后端流程（视觉语言模型方法）

```mermaid
graph TD
    A[PDF输入] --> B[页面图像提取]
    B --> C[VLM模型推理]
    C --> D[Token输出处理]
    D --> E[内容解析]
    E --> F[结构化输出]
```

**关键特点：**
- **端到端处理**：单一模型处理整个页面
- **上下文理解**：更好的文档结构理解能力
- **高性能**：通过SGLang实现极高推理速度

---

## 🚀 解析后端详解

### 1. Pipeline后端

**技术栈：**
- **布局检测**：DocLayout-YOLO (YOLOv10)
- **OCR**：PaddleOCR2Pytorch (支持84种语言)
- **公式识别**：MFD (YOLOv8) + MFR (UniMERNet)
- **表格识别**：RapidTable
- **阅读顺序**：LayoutReader (基于图的排序算法)

**优势：**
- ✅ 高度可配置和可调试
- ✅ 支持CPU模式
- ✅ 语言特定优化
- ✅ 适合特殊文档类型
- ✅ 丰富的调试信息

**系统要求：**
- GPU：6GB+ VRAM (推荐)
- CPU：支持纯CPU模式
- 内存：8GB+

### 2. VLM-Transformers后端

**技术特点：**
- **模型**：定制的MinerU VLM模型（sub-1B参数）
- **能力**：端到端文档理解
- **特性**：多语言识别、手写体、布局分析、表格解析

**优势：**
- ✅ 端到端处理
- ✅ 更好的上下文理解
- ✅ 简化的处理流程

**系统要求：**
- GPU：8GB+ VRAM（必需）
- 仅支持GPU模式

### 3. VLM-SGLang后端

**技术特点：**
- **加速引擎**：SGLang推理加速
- **性能提升**：20-30倍速度提升，单GPU >10,000 tokens/s
- **模式**：Engine模式（本地）和Client-Server模式

**优势：**
- ✅ 最高性能
- ✅ 批处理优化
- ✅ 内存高效
- ✅ 支持分布式处理

**系统要求：**
- GPU：8GB+ VRAM
- 系统：Linux/WSL2
- 用于生产环境高吞吐量场景

---

## 🧠 核心模型详解

### 1. 布局检测模型 (DocLayout-YOLO)

**位置**：`mineru/model/layout/doclayout_yolo.py`

**功能**：识别文档结构元素（标题、段落、表格、图像、公式）

**技术细节**：
- 基于YOLO的目标检测
- 输出边界框和类别标签及置信度
- 支持多种文档布局类型

### 2. OCR模型 (PaddleOCR2Pytorch)

**位置**：`mineru/model/ocr/paddleocr2pytorch/`

**功能**：文本检测和识别

**技术特点**：
- **文本检测**：DBNet算法
- **文字识别**：CRNN架构
- **语言支持**：84种语言
- **模型变体**：PP-OCRv4, PP-OCRv5等

### 3. 公式识别系统

**MFD (数学公式检测)**：
- 位置：`mineru/model/mfd/yolo_v8.py`
- 技术：YOLOv8公式检测
- 输出：公式边界框

**MFR (数学公式识别)**：
- 位置：`mineru/model/mfr/unimernet/`
- 技术：UniMERNet Transformer模型
- 输出：LaTeX格式数学表达式

### 4. 表格识别 (RapidTable)

**位置**：`mineru/model/table/rapid_table.py`

**功能**：表格结构识别和内容提取

**输出**：HTML格式表格结构

### 5. 阅读顺序模型 (LayoutReader)

**位置**：`mineru/model/reading_order/layout_reader.py`

**功能**：确定逻辑阅读顺序

**算法**：基于图的排序算法，考虑空间关系

---

## ⚙️ 配置系统

### 配置文件结构

**主配置文件**：`mineru.template.json`

```json
{
    "bucket_info": {
        "bucket-name-1": ["ak", "sk", "endpoint"]
    },
    "latex-delimiter-config": {
        "display": {"left": "$$", "right": "$$"},
        "inline": {"left": "$", "right": "$"}
    },
    "llm-aided-config": {
        "title_aided": {
            "api_key": "your_api_key",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "model": "qwen2.5-32b-instruct",
            "enable": false
        }
    },
    "models-dir": {
        "pipeline": "",
        "vlm": ""
    },
    "config_version": "1.3.0"
}
```

### 环境变量配置

- `MINERU_DEVICE_MODE`：设备模式（cpu, cuda, npu, mps）
- `MINERU_VIRTUAL_VRAM_SIZE`：虚拟显存大小限制
- `MINERU_MODEL_SOURCE`：模型源（huggingface, modelscope, local）

---

## 📊 数据流和处理逻辑

### 1. 输入处理

**支持格式**：
- PDF文件 (`.pdf`)
- 图像文件 (`.png`, `.jpeg`, `.jpg`, `.webp`, `.gif`)

**预处理流程**：
```python
# mineru/cli/common.py:23
def read_fn(path):
    if path.suffix in image_suffixes:
        return images_bytes_to_pdf_bytes(file_bytes)
    elif path.suffix in pdf_suffixes:
        return file_bytes
```

### 2. 页面图像转换

**核心函数**：`mineru/utils/pdf_reader.py:10`

```python
def page_to_image(page: PdfPage, dpi: int = 144, max_width_or_height: int = 2560):
    # 将PDF页面转换为PIL图像
    # 自动调整分辨率以适应内存限制
```

### 3. 模型单例管理

**设计模式**：`mineru/backend/pipeline/pipeline_analyze.py:17`

```python
class ModelSingleton:
    # 确保每种配置的模型只初始化一次
    # 内存和性能优化
```

---

## 🎨 输出格式说明

### 1. 最终输出

- **Markdown**：人类可读的格式化文本
- **JSON**：程序化访问的结构化数据
- **Images**：提取的图表和图像

### 2. 调试和可视化（仅Pipeline后端）

- **Layout PDF**：可视化布局分析结果
- **Spans PDF**：文本片段可视化
- **Model JSON**：原始模型推理结果
- **Middle JSON**：完整的中间处理数据

### 3. 结构化数据

- **Content List**：简化的阅读顺序内容
- **Model Output**：原始VLM token输出（仅VLM）

---

## 🚀 快速开始指南

### 1. 安装

```bash
# 基础安装
pip install mineru

# 完整功能安装
pip install mineru[all]

# 特定后端安装
pip install mineru[pipeline]  # Pipeline后端
pip install mineru[vlm]       # VLM后端
pip install mineru[sglang]    # SGLang加速
```

### 2. 基本使用

```bash
# 基本PDF解析
mineru -p input.pdf -o output_dir

# 指定后端
mineru -p input.pdf -o output_dir --backend vlm-transformers

# 高性能SGLang后端
mineru -p input.pdf -o output_dir --backend vlm-sglang-engine

# 指定语言和设备
mineru -p input.pdf -o output_dir --lang en --device cuda:0
```

### 3. API服务

```bash
# 启动FastAPI服务
mineru-api

# 启动Gradio Web界面
mineru-gradio

# 启动SGLang服务器
mineru-sglang-server
```

---

## 🔧 开发和扩展

### 1. 添加新的解析后端

1. 在 `mineru/backend/` 创建新目录
2. 实现解析接口
3. 在 `mineru/cli/client.py` 添加后端选项
4. 更新模型管理逻辑

### 2. 集成新的AI模型

1. 在 `mineru/model/` 相应目录添加模型
2. 实现统一的预测接口
3. 更新模型初始化逻辑
4. 添加配置选项

### 3. 扩展输出格式

1. 在处理管道中添加新的格式转换器
2. 更新 `mineru/backend/*/middle_json_mkcontent.py`
3. 添加相应的CLI选项

---

## 📈 性能优化建议

### 1. 硬件配置建议

**Pipeline后端**：
- GPU：6GB+ VRAM (RTX 3060以上)
- CPU：8核以上 (支持纯CPU模式)
- 内存：16GB+

**VLM后端**：
- GPU：8GB+ VRAM (RTX 4070以上)
- 内存：16GB+

**SGLang后端**：
- GPU：8GB+ VRAM (推荐24GB+用于批处理)
- 系统：Linux/WSL2

### 2. 性能调优参数

```bash
# 限制GPU内存使用
mineru -p input.pdf -o output --vram 8000

# 批处理多文件
mineru -p input_dir/ -o output_dir

# 指定页面范围
mineru -p input.pdf -o output --start 0 --end 10
```

---

## 🐛 故障排除

### 1. 常见问题

**内存不足**：
- 降低 `--vram` 参数
- 使用CPU模式：`--device cpu`
- 分批处理大文件

**模型下载失败**：
- 使用 `mineru-models-download`
- 切换模型源：`--source modelscope`
- 检查网络连接

**精度问题**：
- 尝试不同后端
- 调整语言设置：`--lang`
- 启用/禁用特定功能：`--formula`，`--table`

### 2. 日志和调试

```bash
# 启用详细日志
export LOGURU_LEVEL=DEBUG
mineru -p input.pdf -o output

# 检查配置
cat ~/.mineru/mineru.json
```

---

## 🤝 贡献指南

### 1. 开发环境设置

```bash
git clone https://github.com/opendatalab/MinerU.git
cd MinerU
pip install -e .[all]
```

### 2. 测试

```bash
# 运行测试
pytest tests/

# 生成覆盖率报告
python tests/get_coverage.py
```

### 3. 代码规范

- 遵循PEP 8编码规范
- 添加类型注解
- 编写单元测试
- 更新文档

---

## 📚 相关资源

- **项目主页**：https://mineru.net/
- **GitHub仓库**：https://github.com/opendatalab/MinerU
- **文档**：https://opendatalab.github.io/MinerU/
- **论文**：https://arxiv.org/abs/2409.18839
- **演示**：https://huggingface.co/spaces/opendatalab/MinerU

---

## 📄 许可证

本项目采用 AGPL-3.0 许可证。详见 [LICENSE.md](LICENSE.md) 文件。

---

*本文档基于MinerU v2.1.9版本编写，随项目更新可能需要调整。*